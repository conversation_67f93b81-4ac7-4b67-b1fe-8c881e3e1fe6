<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LectoAI - Smart Lecture Assistant</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Markdown rendering and syntax highlighting -->
    <script src="https://cdn.jsdelivr.net/npm/marked@9.1.6/marked.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/dompurify@3.0.5/dist/purify.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/github-dark.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/highlight.min.js"></script>
    <!-- Math rendering -->
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --sidebar-bg: #202123;
            --chat-bg: #343541;
            --user-bubble: #19c37d;
            --ai-bubble: #444654;
            --text-primary: #ececf1;
            --text-secondary: #8e8ea0;
            --accent: #10a37f;
            --input-bg: #40414f;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        body {
            background-color: var(--chat-bg);
            color: var(--text-primary);
            height: 100vh;
            display: flex;
            overflow: hidden;
        }

        /* Sidebar Styles */
        .sidebar {
            width: 260px;
            background-color: var(--sidebar-bg);
            padding: 16px;
            display: flex;
            flex-direction: column;
            height: 100%;
            transition: transform 0.3s ease;
        }

        .logo {
            padding: 16px 8px;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 12px;
            border-bottom: 1px solid #4d4d4f;
        }

        .logo i {
            color: var(--accent);
            font-size: 24px;
        }

        .logo h1 {
            font-size: 20px;
            font-weight: 600;
        }

        .upload-section {
            margin-bottom: 24px;
        }

        .upload-section h2 {
            font-size: 14px;
            margin-bottom: 12px;
            color: var(--text-secondary);
            padding-left: 8px;
        }

        #dropzone {
            border: 2px dashed #4d4d4f;
            border-radius: 8px;
            padding: 24px 16px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
            background-color: rgba(255, 255, 255, 0.03);
        }

        #dropzone:hover {
            background-color: rgba(255, 255, 255, 0.06);
            border-color: var(--accent);
        }

        #dropzone i {
            font-size: 32px;
            margin-bottom: 12px;
            color: var(--text-secondary);
        }

        #dropzone p {
            font-size: 14px;
            color: var(--text-secondary);
        }

        #fileInput {
            display: none;
        }

        .status-bar {
            margin-top: auto;
            padding: 16px 8px;
            border-top: 1px solid #4d4d4f;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }

        .status-indicator {
            width: 10px;
            height: 10px;
            background-color: #10a37f;
            border-radius: 50%;
        }

        /* Main Chat Area */
        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            position: relative;
        }

        .chat-header {
            padding: 16px;
            border-bottom: 1px solid #4d4d4f;
            text-align: center;
            font-size: 14px;
            color: var(--text-secondary);
        }

        #chatMessages {
            flex: 1;
            overflow-y: auto;
            padding: 24px;
            display: flex;
            flex-direction: column;
            gap: 24px;
        }

        .message {
            max-width: 768px;
            margin: 0 auto;
            width: 100%;
            display: flex;
            gap: 24px;
        }

        .avatar {
            width: 36px;
            height: 36px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }

        .user .avatar {
            background-color: var(--user-bubble);
        }

        .ai .avatar {
            background-color: var(--accent);
        }

        .message-content {
            flex: 1;
        }

        .user .message-content {
            color: var(--text-primary);
        }

        .ai .message-content {
            color: var(--text-primary);
        }

        /* Input Area */
        .input-container {
            padding: 16px 24px;
            position: relative;
        }

        .input-box {
            position: relative;
            border-radius: 8px;
            background-color: var(--input-bg);
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
        }

        #userInput {
            width: 100%;
            background: none;
            border: none;
            padding: 16px 48px 16px 16px;
            color: var(--text-primary);
            font-size: 16px;
            resize: none;
            max-height: 200px;
            outline: none;
        }

        #sendBtn {
            position: absolute;
            right: 12px;
            bottom: 12px;
            background: none;
            border: none;
            color: var(--text-secondary);
            cursor: pointer;
            width: 32px;
            height: 32px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        #sendBtn:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: var(--accent);
        }

        .input-footer {
            text-align: center;
            color: var(--text-secondary);
            font-size: 12px;
            margin-top: 8px;
        }

        /* Scrollbar styling */
        #chatMessages::-webkit-scrollbar {
            width: 8px;
        }

        #chatMessages::-webkit-scrollbar-track {
            background: transparent;
        }

        #chatMessages::-webkit-scrollbar-thumb {
            background: #565869;
            border-radius: 4px;
        }

        /* Loading animation */
        .typing-indicator {
            display: inline-block;
            position: relative;
            width: 80px;
            height: 20px;
        }

        .typing-dot {
            position: absolute;
            width: 8px;
            height: 8px;
            background: var(--text-secondary);
            border-radius: 50%;
            animation: typing 1.4s infinite;
        }

        .typing-dot:nth-child(1) { left: 8px; animation-delay: 0s; }
        .typing-dot:nth-child(2) { left: 32px; animation-delay: 0.2s; }
        .typing-dot:nth-child(3) { left: 56px; animation-delay: 0.4s; }

        @keyframes typing {
            0%, 60%, 100% { transform: translateY(0); }
            30% { transform: translateY(-6px); }
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .sidebar {
                position: absolute;
                z-index: 100;
                transform: translateX(-100%);
            }
            .sidebar.active {
                transform: translateX(0);
            }
            .mobile-menu-btn {
                display: block;
                position: absolute;
                top: 16px;
                left: 16px;
                z-index: 90;
            }
        }

        .mobile-menu-btn {
            display: none;
            background: var(--input-bg);
            border: none;
            color: var(--text-primary);
            width: 40px;
            height: 40px;
            border-radius: 4px;
            font-size: 20px;
            cursor: pointer;
            position: absolute;
            top: 16px;
            left: 16px;
            z-index: 90;
        }

        /* Enhanced typography for ChatGPT-like appearance */
        .message-content {
            line-height: 1.7;
            font-size: 15px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
        }

        .message-content h1, .message-content h2, .message-content h3 {
            color: var(--accent);
            margin: 20px 0 12px 0;
            font-weight: 600;
            line-height: 1.3;
        }

        .message-content h1 { font-size: 1.6em; }
        .message-content h2 { font-size: 1.4em; }
        .message-content h3 { font-size: 1.2em; }

        .message-content p {
            margin: 12px 0;
            line-height: 1.7;
        }

        .message-content ul, .message-content ol {
            margin: 12px 0;
            padding-left: 24px;
        }

        .message-content li {
            margin: 4px 0;
            line-height: 1.5;
        }

        .message-content blockquote {
            border-left: 4px solid var(--accent);
            margin: 16px 0;
            padding: 12px 16px;
            background: rgba(16, 163, 127, 0.1);
            border-radius: 0 4px 4px 0;
        }

        .message-content code {
            background: rgba(255, 255, 255, 0.1);
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 0.9em;
        }

        .message-content pre {
            background: rgba(0, 0, 0, 0.3);
            padding: 16px;
            border-radius: 8px;
            overflow-x: auto;
            margin: 12px 0;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .message-content pre code {
            background: none;
            padding: 0;
            border-radius: 0;
        }

        .message-content table {
            border-collapse: collapse;
            width: 100%;
            margin: 16px 0;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            overflow: hidden;
        }

        .message-content th, .message-content td {
            border: 1px solid rgba(255, 255, 255, 0.1);
            padding: 12px;
            text-align: left;
        }

        .message-content th {
            background: rgba(16, 163, 127, 0.2);
            font-weight: 600;
        }

        .message-content tr:nth-child(even) {
            background: rgba(255, 255, 255, 0.05);
        }

        .message-content strong {
            color: var(--accent);
            font-weight: 600;
        }

        .message-content em {
            color: var(--text-secondary);
            font-style: italic;
        }

        /* Progress indicator styles */
        .progress-container {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            height: 4px;
            margin: 8px 0;
            overflow: hidden;
        }

        .progress-bar {
            background: linear-gradient(90deg, var(--accent), #19c37d);
            height: 100%;
            border-radius: 4px;
            transition: width 0.3s ease;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        /* Enhanced loading states */
        .loading-message {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 16px;
            background: rgba(16, 163, 127, 0.1);
            border-radius: 8px;
            border-left: 4px solid var(--accent);
        }

        .loading-spinner {
            width: 20px;
            height: 20px;
            border: 2px solid rgba(16, 163, 127, 0.3);
            border-top: 2px solid var(--accent);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Retry button styles */
        .retry-button {
            background: var(--accent);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-top: 8px;
            transition: background 0.2s;
        }

        .retry-button:hover {
            background: #0d8f6f;
        }

        /* Animation for suggestion buttons */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Enhanced suggestion button styles */
        .suggestion-button {
            position: relative;
            overflow: hidden;
        }

        .suggestion-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left 0.5s;
        }

        .suggestion-button:hover::before {
            left: 100%;
        }

        /* Suggestions container */
        .suggestions-container {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
            margin-top: 8px;
        }

        /* Response action buttons */
        .response-actions {
            display: flex;
            gap: 8px;
            margin-top: 12px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .message:hover .response-actions {
            opacity: 1;
        }

        .action-btn {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 6px;
            padding: 6px 12px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s;
            color: var(--text-secondary);
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .action-btn:hover {
            background: rgba(16, 163, 127, 0.2);
            border-color: var(--accent);
            color: var(--text-primary);
        }

        .action-btn.active {
            background: var(--accent);
            color: white;
        }

        /* Streaming cursor */
        .streaming-cursor {
            display: inline-block;
            width: 2px;
            height: 1.2em;
            background: var(--accent);
            margin-left: 2px;
            animation: blink 1s infinite;
        }

        /* Scroll to bottom button */
        .scroll-to-bottom {
            position: fixed;
            bottom: 120px;
            right: 30px;
            width: 50px;
            height: 50px;
            background: var(--accent);
            border: none;
            border-radius: 50%;
            color: white;
            font-size: 18px;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transform: translateY(20px);
        }

        .scroll-to-bottom.visible {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .scroll-to-bottom:hover {
            background: #0ea5e9;
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.4);
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }

        /* Enhanced message styling */
        .message.ai .message-content {
            background: rgba(255, 255, 255, 0.02);
            border-radius: 12px;
            padding: 16px;
            border: 1px solid rgba(255, 255, 255, 0.05);
        }

        .message.user .message-content {
            background: rgba(16, 163, 127, 0.1);
            border-radius: 12px;
            padding: 16px;
            border: 1px solid rgba(16, 163, 127, 0.2);
        }

        /* Conversation flow indicators */
        .conversation-indicator {
            font-size: 12px;
            color: var(--text-secondary);
            margin: 8px 0;
            text-align: center;
            opacity: 0.7;
        }

        /* Copy notification */
        .copy-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--accent);
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            font-size: 14px;
            z-index: 1000;
            animation: slideIn 0.3s ease;
        }

        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
    </style>
</head>
<body>
    <!-- Mobile menu button -->
    <button class="mobile-menu-btn" id="menuBtn">
        <i class="fas fa-bars"></i>
    </button>

    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
        <div class="logo">
            <i class="fas fa-graduation-cap"></i>
            <h1>LectoAI</h1>
        </div>
        
        <div class="upload-section">
            <h2>Upload Lecture Notes</h2>
            <div id="dropzone">
                <i class="fas fa-cloud-upload-alt"></i>
                <p>Drop files or click to upload</p>
                <p class="small">(PDF, DOCX, PPT, PNG, JPG)</p>
                <input type="file" id="fileInput" multiple accept=".pdf,.docx,.pptx,.png,.jpg">
            </div>
        </div>
        
        <div class="status-bar">
            <div class="status-indicator"></div>
            <span>AI Assistant Ready</span>
        </div>
    </div>

    <!-- Main Chat Area -->
    <div class="chat-container">
        <div class="chat-header">
            <p>Ask questions about your lecture notes</p>
        </div>
        
        <div id="chatMessages">
            <div class="message ai">
                <div class="avatar">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="message-content">
                    <p>Hello! I'm LectoAI, your lecture notes assistant. Upload your files (PDF, DOCX, PPT, images) and I'll help you understand them. Ask me anything about your course materials!</p>
                </div>
            </div>
        </div>
        
        <div class="input-container">
            <div class="input-box">
                <textarea 
                    id="userInput" 
                    placeholder="Ask about your lecture notes..." 
                    rows="1"
                ></textarea>
                <button id="sendBtn">
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
            <div class="input-footer">
                LectoAI can explain concepts, summarize content, and answer questions about your materials
            </div>
        </div>
    </div>

    <!-- Scroll to bottom button -->
    <button class="scroll-to-bottom" id="scrollToBottomBtn" title="Scroll to bottom">
        <i class="fas fa-chevron-down"></i>
    </button>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // Configure marked for markdown rendering
            marked.setOptions({
                highlight: function(code, lang) {
                    if (lang && hljs.getLanguage(lang)) {
                        try {
                            return hljs.highlight(code, { language: lang }).value;
                        } catch (err) {}
                    }
                    return hljs.highlightAuto(code).value;
                },
                breaks: true,
                gfm: true
            });

            // Configure MathJax
            window.MathJax = {
                tex: {
                    inlineMath: [['$', '$'], ['\\(', '\\)']],
                    displayMath: [['$$', '$$'], ['\\[', '\\]']]
                },
                svg: {
                    fontCache: 'global'
                }
            };

            // DOM Elements
            const dropzone = document.getElementById('dropzone');
            const fileInput = document.getElementById('fileInput');
            const chatMessages = document.getElementById('chatMessages');
            const userInput = document.getElementById('userInput');
            const sendBtn = document.getElementById('sendBtn');
            const menuBtn = document.getElementById('menuBtn');
            const sidebar = document.getElementById('sidebar');
            const scrollToBottomBtn = document.getElementById('scrollToBottomBtn');

            // State variables
            let lectureContext = "";
            let isProcessing = false;
            let currentFileName = "";
            let sessionId = generateSessionId();
            let conversationCount = 0;
            const API_BASE_URL = 'http://localhost:5000';

            // Smart scrolling state
            let isUserScrolling = false;
            let scrollTimeout = null;
            let isAtBottom = true;
            let lastScrollTop = 0;

            // Generate unique session ID for conversation memory
            function generateSessionId() {
                return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            }

            // Smart scrolling management
            function initializeSmartScrolling() {
                // Check if user is at bottom of chat
                function checkIfAtBottom() {
                    const threshold = 50; // pixels from bottom
                    const scrollTop = chatMessages.scrollTop;
                    const scrollHeight = chatMessages.scrollHeight;
                    const clientHeight = chatMessages.clientHeight;

                    isAtBottom = (scrollHeight - scrollTop - clientHeight) <= threshold;
                    return isAtBottom;
                }

                // Handle scroll events
                chatMessages.addEventListener('scroll', function() {
                    const currentScrollTop = chatMessages.scrollTop;

                    // Detect if user is manually scrolling (not programmatic)
                    if (Math.abs(currentScrollTop - lastScrollTop) > 5) {
                        isUserScrolling = true;

                        // Clear existing timeout
                        if (scrollTimeout) {
                            clearTimeout(scrollTimeout);
                        }

                        // Reset user scrolling flag after a delay
                        scrollTimeout = setTimeout(() => {
                            isUserScrolling = false;
                        }, 1000);
                    }

                    // Update bottom detection
                    checkIfAtBottom();

                    // Show/hide scroll to bottom button
                    if (isAtBottom) {
                        scrollToBottomBtn.classList.remove('visible');
                    } else {
                        scrollToBottomBtn.classList.add('visible');
                    }

                    lastScrollTop = currentScrollTop;
                });

                // Initial check
                checkIfAtBottom();
            }

            // Smart auto-scroll function
            function smartAutoScroll(force = false) {
                // Only auto-scroll if user is at bottom or force is true
                if (force || (isAtBottom && !isUserScrolling)) {
                    chatMessages.scrollTop = chatMessages.scrollHeight;
                    isAtBottom = true;
                    scrollToBottomBtn.classList.remove('visible');
                }
            }

            // Smooth scroll to bottom (for user-initiated actions)
            function smoothScrollToBottom() {
                isUserScrolling = false; // Allow auto-scroll
                chatMessages.scrollTo({
                    top: chatMessages.scrollHeight,
                    behavior: 'smooth'
                });
                isAtBottom = true;
                scrollToBottomBtn.classList.remove('visible');
            }
            
            // Initialize smart scrolling
            initializeSmartScrolling();

            // Scroll to bottom button handler
            scrollToBottomBtn.addEventListener('click', () => {
                smoothScrollToBottom();
                scrollToBottomBtn.classList.remove('visible');
            });

            // Auto-resize textarea
            userInput.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = (this.scrollHeight) + 'px';
            });
            
            // Mobile menu toggle
            menuBtn.addEventListener('click', () => {
                sidebar.classList.toggle('active');
            });
            
            // File upload handling
            dropzone.addEventListener('click', () => fileInput.click());
            
            fileInput.addEventListener('change', async (e) => {
                if (e.target.files.length === 0) return;

                const file = e.target.files[0];

                // Validate file size (max 10MB)
                if (file.size > 10 * 1024 * 1024) {
                    addMessage("File too large. Please select a file smaller than 10MB.", 'error');
                    return;
                }

                addMessage(`Uploading ${file.name}...`, 'status');

                // Create a loading indicator
                const loadingMsg = addMessage("Processing file...", 'ai', true);

                try {
                    // Create FormData for file upload
                    const formData = new FormData();
                    formData.append('file', file);

                    // Upload to backend
                    const response = await fetch(`${API_BASE_URL}/upload`, {
                        method: 'POST',
                        body: formData
                    });

                    const result = await response.json();

                    // Remove loading indicator
                    loadingMsg.remove();

                    if (response.ok && result.success) {
                        // Success message
                        addMessage(`✅ Successfully processed **${file.name}**! I've analyzed your lecture notes (${result.text_length} characters extracted). Now you can ask me questions about this material.`, 'ai');

                        // Store the extracted text as context
                        lectureContext = result.text;
                        currentFileName = file.name;

                        // Show preview of extracted content
                        if (result.preview) {
                            addMessage(`📄 **Content Preview:**\n\n> ${result.preview}`, 'ai');
                        }

                        // Generate dynamic questions based on content
                        setTimeout(async () => {
                            await generateDynamicQuestions();
                        }, 500);

                        // Add conversation starter
                        setTimeout(() => {
                            addMessage("💡 **I'm ready to help you learn!** Feel free to ask me anything about this material, or try one of the suggested questions below. I'll remember our conversation and can build on previous topics we discuss.", 'ai', false, true);
                        }, 1000);

                    } else {
                        addMessage(`Upload failed: ${result.error || 'Unknown error'}`, 'error');
                    }

                } catch (error) {
                    loadingMsg.remove();
                    addMessage(`Upload failed: ${error.message}`, 'error');
                }
            });
            
            // ChatGPT-like streaming message functionality
            async function sendMessage(retryCount = 0) {
                const question = userInput.value.trim();
                if (!question || isProcessing) return;

                // Add conversation indicator if this isn't the first message
                if (conversationCount > 0) {
                    addConversationIndicator();
                }

                // Add user's message to chat
                addMessage(question, 'user');
                userInput.value = '';
                userInput.style.height = 'auto';
                conversationCount++;

                isProcessing = true;

                // Create streaming message container
                const streamingMsg = addStreamingMessage();

                try {
                    // Use streaming endpoint for real-time responses
                    const response = await fetch(`${API_BASE_URL}/ask-stream`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            question: question,
                            context: lectureContext,
                            session_id: sessionId
                        })
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }

                    const reader = response.body.getReader();
                    const decoder = new TextDecoder();
                    let fullResponse = '';
                    let buffer = '';

                    while (true) {
                        const { done, value } = await reader.read();

                        if (done) break;

                        buffer += decoder.decode(value, { stream: true });
                        const lines = buffer.split('\n');
                        buffer = lines.pop(); // Keep incomplete line in buffer

                        for (const line of lines) {
                            if (line.startsWith('data: ')) {
                                try {
                                    const data = JSON.parse(line.slice(6));

                                    if (data.error) {
                                        throw new Error(data.error);
                                    }

                                    if (data.token) {
                                        fullResponse += data.token;
                                        updateStreamingMessage(streamingMsg, fullResponse, !data.done);
                                    }

                                    if (data.done) {
                                        finalizeStreamingMessage(streamingMsg, fullResponse, data.cached);
                                        break;
                                    }
                                } catch (parseError) {
                                    console.warn('Failed to parse SSE data:', parseError);
                                }
                            }
                        }
                    }

                } catch (error) {
                    // Remove streaming message and show error
                    streamingMsg.remove();

                    if (retryCount < 2) {
                        addRetryMessage(error.message, question, retryCount);
                    } else {
                        addMessage(`❌ **Connection Error:** ${error.message}`, 'error');

                        // Fallback to demo response
                        setTimeout(() => {
                            addMessage("🔄 **Offline Mode:** Here's a basic response:", 'ai');
                            const fallbackResponse = generateDemoResponse(question);
                            addMessage(fallbackResponse, 'ai', false, true);
                        }, 1000);
                    }

                } finally {
                    isProcessing = false;
                }
            }

            // Add conversation flow indicator
            function addConversationIndicator() {
                const indicator = document.createElement('div');
                indicator.className = 'conversation-indicator';
                indicator.textContent = '💬 Continuing conversation...';
                chatMessages.appendChild(indicator);
                smartAutoScroll();
            }

            // Create streaming message container
            function addStreamingMessage() {
                const messageDiv = document.createElement('div');
                messageDiv.classList.add('message', 'ai');

                const avatar = document.createElement('div');
                avatar.classList.add('avatar');
                avatar.innerHTML = '<i class="fas fa-robot"></i>';

                const content = document.createElement('div');
                content.classList.add('message-content');
                content.innerHTML = '<span class="streaming-cursor"></span>';

                messageDiv.appendChild(avatar);
                messageDiv.appendChild(content);
                chatMessages.appendChild(messageDiv);

                smartAutoScroll();
                return messageDiv;
            }

            // Update streaming message with new content
            function updateStreamingMessage(messageDiv, text, isStreaming) {
                const content = messageDiv.querySelector('.message-content');
                const cursor = isStreaming ? '<span class="streaming-cursor"></span>' : '';

                try {
                    const htmlContent = marked.parse(text);
                    const sanitizedContent = DOMPurify.sanitize(htmlContent);
                    content.innerHTML = sanitizedContent + cursor;

                    // Highlight code blocks
                    content.querySelectorAll('pre code').forEach((block) => {
                        hljs.highlightElement(block);
                    });

                    // Render math if present
                    if (window.MathJax && (text.includes('$') || text.includes('\\('))) {
                        MathJax.typesetPromise([content]).catch((err) => console.log(err));
                    }
                } catch (error) {
                    content.innerHTML = text.replace(/\n/g, '<br>') + cursor;
                }

                smartAutoScroll();
            }

            // Finalize streaming message and add actions
            function finalizeStreamingMessage(messageDiv, text, isCached) {
                const content = messageDiv.querySelector('.message-content');

                try {
                    const htmlContent = marked.parse(text);
                    const sanitizedContent = DOMPurify.sanitize(htmlContent);
                    content.innerHTML = sanitizedContent;

                    // Highlight code blocks
                    content.querySelectorAll('pre code').forEach((block) => {
                        hljs.highlightElement(block);
                    });

                    // Render math if present
                    if (window.MathJax && (text.includes('$') || text.includes('\\('))) {
                        MathJax.typesetPromise([content]).catch((err) => console.log(err));
                    }
                } catch (error) {
                    content.innerHTML = text.replace(/\n/g, '<br>');
                }

                // Add response actions
                addResponseActions(messageDiv, text, isCached);

                // Add suggested questions after AI responses (limit to 3)
                setTimeout(() => {
                    addContextualSuggestedQuestions(text);
                }, 500);

                smartAutoScroll();
            }

            // Add enhanced loading message
            function addLoadingMessage(text) {
                const messageDiv = document.createElement('div');
                messageDiv.classList.add('message', 'ai');

                const avatar = document.createElement('div');
                avatar.classList.add('avatar');
                avatar.innerHTML = '<i class="fas fa-robot"></i>';

                const content = document.createElement('div');
                content.classList.add('message-content');
                content.innerHTML = `
                    <div class="loading-message">
                        <div class="loading-spinner"></div>
                        <span>${text}</span>
                    </div>
                    <div class="progress-container">
                        <div class="progress-bar" style="width: 0%"></div>
                    </div>
                `;

                messageDiv.appendChild(avatar);
                messageDiv.appendChild(content);
                chatMessages.appendChild(messageDiv);

                // Animate progress bar
                const progressBar = content.querySelector('.progress-bar');
                let progress = 0;
                const interval = setInterval(() => {
                    progress += Math.random() * 15;
                    if (progress > 90) progress = 90;
                    progressBar.style.width = progress + '%';
                }, 500);

                messageDiv.stopProgress = () => clearInterval(interval);

                smartAutoScroll();
                return messageDiv;
            }

            // Add retry message with button
            function addRetryMessage(errorMsg, originalQuestion, retryCount) {
                const messageDiv = document.createElement('div');
                messageDiv.classList.add('message', 'error');

                const avatar = document.createElement('div');
                avatar.classList.add('avatar');
                avatar.innerHTML = '<i class="fas fa-exclamation-triangle"></i>';
                avatar.style.backgroundColor = '#dc3545';

                const content = document.createElement('div');
                content.classList.add('message-content');
                content.innerHTML = `
                    <p style="color: #ff6b6b;">⚠️ ${errorMsg}</p>
                    <button class="retry-button" onclick="retryQuestion('${originalQuestion.replace(/'/g, "\\'")}', ${retryCount + 1})">
                        🔄 Retry (${2 - retryCount} attempts left)
                    </button>
                `;

                messageDiv.appendChild(avatar);
                messageDiv.appendChild(content);
                chatMessages.appendChild(messageDiv);
                smartAutoScroll();
            }

            // Add response action buttons
            function addResponseActions(messageDiv, text, isCached) {
                const content = messageDiv.querySelector('.message-content');

                const actionsDiv = document.createElement('div');
                actionsDiv.className = 'response-actions';

                // Copy button
                const copyBtn = document.createElement('button');
                copyBtn.className = 'action-btn';
                copyBtn.innerHTML = '<i class="fas fa-copy"></i> Copy';
                copyBtn.onclick = () => copyToClipboard(text, copyBtn);

                // Regenerate button
                const regenBtn = document.createElement('button');
                regenBtn.className = 'action-btn';
                regenBtn.innerHTML = '<i class="fas fa-redo"></i> Regenerate';
                regenBtn.onclick = () => regenerateResponse();

                // Thumbs up/down
                const thumbsUpBtn = document.createElement('button');
                thumbsUpBtn.className = 'action-btn';
                thumbsUpBtn.innerHTML = '<i class="fas fa-thumbs-up"></i>';
                thumbsUpBtn.onclick = () => toggleFeedback(thumbsUpBtn, 'up');

                const thumbsDownBtn = document.createElement('button');
                thumbsDownBtn.className = 'action-btn';
                thumbsDownBtn.innerHTML = '<i class="fas fa-thumbs-down"></i>';
                thumbsDownBtn.onclick = () => toggleFeedback(thumbsDownBtn, 'down');

                // Share button
                const shareBtn = document.createElement('button');
                shareBtn.className = 'action-btn';
                shareBtn.innerHTML = '<i class="fas fa-share"></i> Share';
                shareBtn.onclick = () => shareResponse(text);

                // Add cached indicator
                if (isCached) {
                    const cachedIndicator = document.createElement('span');
                    cachedIndicator.className = 'action-btn';
                    cachedIndicator.innerHTML = '<i class="fas fa-bolt"></i> Instant';
                    cachedIndicator.style.background = 'rgba(255, 193, 7, 0.2)';
                    cachedIndicator.style.color = '#ffc107';
                    cachedIndicator.style.cursor = 'default';
                    actionsDiv.appendChild(cachedIndicator);
                }

                actionsDiv.appendChild(copyBtn);
                actionsDiv.appendChild(regenBtn);
                actionsDiv.appendChild(thumbsUpBtn);
                actionsDiv.appendChild(thumbsDownBtn);
                actionsDiv.appendChild(shareBtn);

                content.appendChild(actionsDiv);
            }

            // Copy to clipboard with notification
            async function copyToClipboard(text, button) {
                try {
                    await navigator.clipboard.writeText(text);

                    // Visual feedback
                    const originalText = button.innerHTML;
                    button.innerHTML = '<i class="fas fa-check"></i> Copied!';
                    button.classList.add('active');

                    // Show notification
                    showNotification('Response copied to clipboard!');

                    setTimeout(() => {
                        button.innerHTML = originalText;
                        button.classList.remove('active');
                    }, 2000);
                } catch (err) {
                    showNotification('Failed to copy to clipboard', 'error');
                }
            }

            // Show notification
            function showNotification(message, type = 'success') {
                const notification = document.createElement('div');
                notification.className = 'copy-notification';
                notification.textContent = message;

                if (type === 'error') {
                    notification.style.background = '#dc3545';
                }

                document.body.appendChild(notification);

                setTimeout(() => {
                    notification.style.animation = 'slideIn 0.3s ease reverse';
                    setTimeout(() => notification.remove(), 300);
                }, 3000);
            }

            // Toggle feedback buttons
            function toggleFeedback(button, type) {
                const isActive = button.classList.contains('active');

                // Remove active state from both feedback buttons
                const messageDiv = button.closest('.message');
                const feedbackBtns = messageDiv.querySelectorAll('.action-btn');
                feedbackBtns.forEach(btn => {
                    if (btn.innerHTML.includes('thumbs')) {
                        btn.classList.remove('active');
                    }
                });

                // Toggle current button
                if (!isActive) {
                    button.classList.add('active');
                    showNotification(`Feedback recorded: ${type === 'up' ? 'Helpful' : 'Not helpful'}`);
                }
            }

            // Regenerate response with proper formatting
            function regenerateResponse() {
                const lastUserMessage = [...chatMessages.querySelectorAll('.message.user')].pop();
                if (lastUserMessage) {
                    // Get the question text
                    const questionElement = lastUserMessage.querySelector('.message-content p') ||
                                          lastUserMessage.querySelector('.message-content');
                    const question = questionElement.textContent.trim();

                    // Remove the last AI response(s) to avoid duplication
                    const aiMessages = [...chatMessages.querySelectorAll('.message.ai')];
                    const lastAiMessage = aiMessages.pop();
                    if (lastAiMessage) {
                        lastAiMessage.remove();
                    }

                    // Also remove any suggestion buttons that might be after the AI message
                    const suggestions = [...chatMessages.querySelectorAll('.suggestions-container')];
                    const lastSuggestion = suggestions.pop();
                    if (lastSuggestion) {
                        lastSuggestion.remove();
                    }

                    // Set the question and send with proper formatting
                    userInput.value = question;
                    sendMessage();
                }
            }

            // Share response
            async function shareResponse(text) {
                if (navigator.share) {
                    try {
                        await navigator.share({
                            title: 'LectoAI Response',
                            text: text
                        });
                    } catch (err) {
                        copyToClipboard(text, event.target);
                    }
                } else {
                    copyToClipboard(text, event.target);
                }
            }

            // Global retry function
            window.retryQuestion = function(question, retryCount) {
                userInput.value = question;
                sendMessage(retryCount);
            };
            
            // Send message on button click
            sendBtn.addEventListener('click', sendMessage);
            
            // Send message on Enter (without Shift)
            userInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });
            
            // Enhanced add message function with markdown support
            function addMessage(text, sender, isTyping = false, useMarkdown = false) {
                const messageDiv = document.createElement('div');
                messageDiv.classList.add('message', sender);

                // Create avatar
                const avatar = document.createElement('div');
                avatar.classList.add('avatar');

                if (sender === 'user') {
                    avatar.innerHTML = '<i class="fas fa-user"></i>';
                } else if (sender === 'error') {
                    avatar.innerHTML = '<i class="fas fa-exclamation-triangle"></i>';
                    avatar.style.backgroundColor = '#dc3545';
                } else if (sender === 'status') {
                    avatar.innerHTML = '<i class="fas fa-info-circle"></i>';
                    avatar.style.backgroundColor = '#17a2b8';
                } else {
                    avatar.innerHTML = '<i class="fas fa-robot"></i>';
                }

                // Create message content
                const content = document.createElement('div');
                content.classList.add('message-content');

                if (isTyping) {
                    const typingIndicator = document.createElement('div');
                    typingIndicator.classList.add('typing-indicator');
                    typingIndicator.innerHTML = `
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                    `;
                    content.appendChild(typingIndicator);
                } else {
                    if (useMarkdown && sender === 'ai') {
                        // Render markdown for AI responses
                        try {
                            const htmlContent = marked.parse(text);
                            const sanitizedContent = DOMPurify.sanitize(htmlContent);
                            content.innerHTML = sanitizedContent;

                            // Highlight code blocks
                            content.querySelectorAll('pre code').forEach((block) => {
                                hljs.highlightElement(block);
                            });

                            // Render math if present
                            if (window.MathJax && (text.includes('$') || text.includes('\\('))) {
                                MathJax.typesetPromise([content]).catch((err) => console.log(err));
                            }
                        } catch (error) {
                            console.error('Markdown rendering error:', error);
                            // Fallback to plain text
                            const formattedText = text.replace(/\n/g, '<br>');
                            content.innerHTML = `<p>${formattedText}</p>`;
                        }
                    } else {
                        // Handle multiline text and preserve formatting
                        const formattedText = text.replace(/\n/g, '<br>');
                        content.innerHTML = `<p>${formattedText}</p>`;
                    }

                    // Style error messages
                    if (sender === 'error') {
                        content.style.color = '#ff6b6b';
                    } else if (sender === 'status') {
                        content.style.color = '#4ecdc4';
                    }
                }

                // Assemble message
                messageDiv.appendChild(avatar);
                messageDiv.appendChild(content);
                chatMessages.appendChild(messageDiv);

                // Smart scroll to bottom
                setTimeout(() => {
                    smoothScrollToBottom();
                }, 100);

                return messageDiv;
            }
            
            // Generate demo responses based on question
            function generateDemoResponse(question) {
                const lowerQ = question.toLowerCase();
                
                if (lowerQ.includes('sort') || lowerQ.includes('algorithm')) {
                    return "Sorting algorithms are methods for reorganizing data in ascending or descending order. Common algorithms include Bubble Sort (O(n²)), Merge Sort (O(n log n)), and Quick Sort (O(n log n) average case). The choice depends on data size and structure.";
                } else if (lowerQ.includes('big o') || lowerQ.includes('complexity')) {
                    return "Big O notation describes algorithm efficiency in terms of time or space complexity as input size grows. O(1) is constant time, O(n) is linear, O(log n) is logarithmic, and O(n²) is quadratic. We use it to compare algorithm scalability.";
                } else if (lowerQ.includes('recursion') || lowerQ.includes('recursive')) {
                    return "Recursion is when a function calls itself to solve smaller instances of the same problem. It requires a base case to terminate and a recursive case that reduces the problem size. Used in tree traversals, divide-and-conquer algorithms, and factorial calculations.";
                } else if (lowerQ.includes('data struct')) {
                    return "Data structures organize data for efficient access. Arrays offer O(1) access but fixed size. Linked lists have O(n) access but dynamic sizing. Stacks (LIFO) and queues (FIFO) control access order. Trees enable hierarchical data, and hash tables provide key-value storage.";
                } else {
                    return "Based on your lecture notes, here's what I understand: Algorithms are step-by-step procedures for calculations. They're fundamental to computer science for solving problems efficiently. Key aspects include correctness, efficiency (time/space complexity), and optimality. Common algorithm design techniques include divide-and-conquer, dynamic programming, and greedy approaches.";
                }
            }
            
            // Generate dynamic questions based on document content
            async function generateDynamicQuestions() {
                if (!lectureContext) {
                    addSuggestedQuestions(); // Fallback to static questions
                    return;
                }

                try {
                    addMessage("🔍 **Analyzing your document to generate relevant questions...**", 'ai');

                    const response = await fetch(`${API_BASE_URL}/generate-questions`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            context: lectureContext
                        })
                    });

                    const result = await response.json();

                    if (response.ok && result.success && result.questions.length > 0) {
                        const questionText = result.fallback ?
                            "💡 **Here are some relevant questions about your content:**" :
                            "🎯 **Here are specific questions I generated based on your document:**";

                        addMessage(questionText, 'ai');
                        addSuggestedQuestions(result.questions);
                    } else {
                        // Fallback to static questions
                        addMessage("💡 **Here are some questions you might ask:**", 'ai');
                        addSuggestedQuestions();
                    }

                } catch (error) {
                    console.error('Error generating questions:', error);
                    addMessage("💡 **Here are some questions you might ask:**", 'ai');
                    addSuggestedQuestions();
                }
            }

            // Add suggested questions (now supports dynamic questions)
            function addSuggestedQuestions(customQuestions = null) {
                const questions = customQuestions || [
                    "Explain the main concepts covered",
                    "What are the key takeaways?",
                    "How do these ideas apply in practice?",
                    "What are some examples of these concepts?",
                    "Summarize the most important points"
                ];
                
                const suggestionsDiv = document.createElement('div');
                suggestionsDiv.classList.add('message', 'ai');
                suggestionsDiv.style.marginTop = '10px';
                
                const avatar = document.createElement('div');
                avatar.classList.add('avatar');
                avatar.innerHTML = '<i class="fas fa-robot"></i>';
                
                const content = document.createElement('div');
                content.classList.add('message-content');
                
                const container = document.createElement('div');
                container.style.display = 'flex';
                container.style.flexWrap = 'wrap';
                container.style.gap = '8px';
                container.style.marginTop = '8px';
                
                questions.forEach((q, index) => {
                    const button = document.createElement('button');
                    button.textContent = q;
                    button.className = 'suggestion-button';
                    button.style.cssText = `
                        background: linear-gradient(135deg, rgba(16, 163, 127, 0.1), rgba(25, 195, 125, 0.1));
                        border: 1px solid rgba(16, 163, 127, 0.3);
                        border-radius: 8px;
                        padding: 10px 16px;
                        font-size: 14px;
                        cursor: pointer;
                        transition: all 0.3s ease;
                        color: var(--text-primary);
                        margin: 4px;
                        display: inline-block;
                        max-width: 100%;
                        text-align: left;
                        line-height: 1.4;
                        animation: fadeInUp 0.5s ease ${index * 0.1}s both;
                    `;

                    button.addEventListener('mouseenter', () => {
                        button.style.background = 'linear-gradient(135deg, rgba(16, 163, 127, 0.2), rgba(25, 195, 125, 0.2))';
                        button.style.borderColor = 'var(--accent)';
                        button.style.transform = 'translateY(-2px)';
                        button.style.boxShadow = '0 4px 12px rgba(16, 163, 127, 0.2)';
                    });

                    button.addEventListener('mouseleave', () => {
                        button.style.background = 'linear-gradient(135deg, rgba(16, 163, 127, 0.1), rgba(25, 195, 125, 0.1))';
                        button.style.borderColor = 'rgba(16, 163, 127, 0.3)';
                        button.style.transform = 'translateY(0)';
                        button.style.boxShadow = 'none';
                    });

                    button.addEventListener('click', () => {
                        // Remove all existing suggestions when a question is clicked
                        const allSuggestions = document.querySelectorAll('.suggestions-container');
                        allSuggestions.forEach(s => s.parentElement.remove());

                        // Add click animation
                        button.style.transform = 'scale(0.95)';
                        setTimeout(() => {
                            button.style.transform = 'translateY(0)';
                            userInput.value = q;
                            userInput.dispatchEvent(new Event('input'));
                            sendMessage();
                        }, 150);
                    });

                    container.appendChild(button);
                });
                
                content.appendChild(container);
                suggestionsDiv.appendChild(avatar);
                suggestionsDiv.appendChild(content);
                chatMessages.appendChild(suggestionsDiv);

                // Smart scroll to bottom
                smartAutoScroll();
            }

            // Add contextual suggested questions after AI responses (limited to 3)
            function addContextualSuggestedQuestions(responseText) {
                // Don't add suggestions if there's no context or if this is an upload response
                if (!lectureContext || responseText.includes('Successfully processed') || responseText.includes('Upload')) {
                    return;
                }

                // Generate contextual questions based on the response content
                const contextualQuestions = generateContextualQuestions(responseText);

                // Limit to 3 questions maximum
                const limitedQuestions = contextualQuestions.slice(0, 3);

                if (limitedQuestions.length > 0) {
                    const suggestionsDiv = document.createElement('div');
                    suggestionsDiv.classList.add('message', 'ai');
                    suggestionsDiv.style.marginTop = '8px';
                    suggestionsDiv.style.opacity = '0.9';

                    const avatar = document.createElement('div');
                    avatar.classList.add('avatar');
                    avatar.innerHTML = '<i class="fas fa-lightbulb"></i>';
                    avatar.style.backgroundColor = 'rgba(16, 163, 127, 0.8)';

                    const content = document.createElement('div');
                    content.classList.add('message-content');

                    const header = document.createElement('p');
                    header.innerHTML = '<strong>💡 Follow-up questions:</strong>';
                    header.style.marginBottom = '8px';
                    header.style.fontSize = '14px';
                    header.style.color = 'var(--text-secondary)';
                    content.appendChild(header);

                    const container = document.createElement('div');
                    container.className = 'suggestions-container';
                    container.style.display = 'flex';
                    container.style.flexWrap = 'wrap';
                    container.style.gap = '6px';

                    limitedQuestions.forEach((q, index) => {
                        const button = document.createElement('button');
                        button.textContent = q;
                        button.className = 'suggestion-button';
                        button.style.cssText = `
                            background: linear-gradient(135deg, rgba(16, 163, 127, 0.08), rgba(25, 195, 125, 0.08));
                            border: 1px solid rgba(16, 163, 127, 0.25);
                            border-radius: 6px;
                            padding: 8px 12px;
                            font-size: 13px;
                            cursor: pointer;
                            transition: all 0.3s ease;
                            color: var(--text-primary);
                            margin: 3px;
                            display: inline-block;
                            max-width: 100%;
                            text-align: left;
                            line-height: 1.3;
                            animation: fadeInUp 0.4s ease ${index * 0.1}s both;
                        `;

                        button.addEventListener('mouseenter', () => {
                            button.style.background = 'linear-gradient(135deg, rgba(16, 163, 127, 0.15), rgba(25, 195, 125, 0.15))';
                            button.style.borderColor = 'rgba(16, 163, 127, 0.4)';
                            button.style.transform = 'translateY(-1px)';
                        });

                        button.addEventListener('mouseleave', () => {
                            button.style.background = 'linear-gradient(135deg, rgba(16, 163, 127, 0.08), rgba(25, 195, 125, 0.08))';
                            button.style.borderColor = 'rgba(16, 163, 127, 0.25)';
                            button.style.transform = 'translateY(0)';
                        });

                        button.addEventListener('click', () => {
                            // Remove all existing suggestions when a question is clicked
                            const allSuggestions = document.querySelectorAll('.suggestions-container');
                            allSuggestions.forEach(s => s.parentElement.remove());

                            // Add click animation
                            button.style.transform = 'scale(0.95)';
                            setTimeout(() => {
                                userInput.value = q;
                                sendMessage();
                            }, 150);
                        });

                        container.appendChild(button);
                    });

                    content.appendChild(container);
                    suggestionsDiv.appendChild(avatar);
                    suggestionsDiv.appendChild(content);
                    chatMessages.appendChild(suggestionsDiv);
                    smartAutoScroll();
                }
            }

            // Generate contextual questions based on response content
            function generateContextualQuestions(responseText) {
                const questions = [];
                const text = responseText.toLowerCase();

                // Extract key concepts from the response
                const concepts = extractKeyConcepts(responseText);

                // Generate different types of questions based on content
                if (concepts.length > 0) {
                    const mainConcept = concepts[0];
                    questions.push(`Can you give me more examples of ${mainConcept}?`);

                    if (concepts.length > 1) {
                        questions.push(`How does ${concepts[0]} relate to ${concepts[1]}?`);
                    }
                }

                // Add context-specific questions
                if (text.includes('algorithm') || text.includes('method') || text.includes('process')) {
                    questions.push("What are the steps involved in this process?");
                    questions.push("What are the advantages and disadvantages?");
                }

                if (text.includes('example') || text.includes('instance')) {
                    questions.push("Can you provide more real-world applications?");
                }

                if (text.includes('theory') || text.includes('concept') || text.includes('principle')) {
                    questions.push("How is this applied in practice?");
                    questions.push("What are the key takeaways?");
                }

                if (text.includes('formula') || text.includes('equation') || text.includes('calculation')) {
                    questions.push("Can you walk through a calculation example?");
                }

                if (text.includes('history') || text.includes('development') || text.includes('evolution')) {
                    questions.push("What led to this development?");
                    questions.push("How has this changed over time?");
                }

                // Fallback questions if no specific patterns found
                if (questions.length === 0) {
                    questions.push("Can you explain this in simpler terms?");
                    questions.push("What are some practical applications?");
                    questions.push("How does this connect to other topics?");
                }

                // Remove duplicates and return
                return [...new Set(questions)];
            }

            // Extract key concepts from text (simple implementation)
            function extractKeyConcepts(text) {
                // Remove markdown and get clean text
                const cleanText = text.replace(/[#*`_\[\]()]/g, '');

                // Find capitalized terms and important phrases
                const concepts = [];
                const words = cleanText.split(/\s+/);

                for (let i = 0; i < words.length; i++) {
                    const word = words[i];
                    // Look for capitalized words that aren't at sentence start
                    if (word.length > 3 && /^[A-Z][a-z]+/.test(word) && i > 0) {
                        const prevWord = words[i-1];
                        if (!prevWord.endsWith('.') && !prevWord.endsWith('!') && !prevWord.endsWith('?')) {
                            concepts.push(word.toLowerCase());
                        }
                    }

                    // Look for multi-word technical terms
                    if (i < words.length - 1) {
                        const twoWords = `${word} ${words[i+1]}`.toLowerCase();
                        if (twoWords.includes('machine learning') || twoWords.includes('neural network') ||
                            twoWords.includes('data structure') || twoWords.includes('algorithm design')) {
                            concepts.push(twoWords);
                        }
                    }
                }

                return [...new Set(concepts)].slice(0, 3); // Return unique concepts, max 3
            }
        });
    </script>
</body>
</html>